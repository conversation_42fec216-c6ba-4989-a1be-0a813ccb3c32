import { IconCopy } from "@douyinfe/semi-icons";
import { Button, Col, Form, Input, Row, Toast } from "@douyinfe/semi-ui";
import { ISON_TYPE_MAP } from "components";
import copy from "copy-to-clipboard";
import React from "react";
import { LandrayOASSOCallbackRoute } from "utils/routerConstants";

export const LandrayOAAuthForm: React.FC = () => {
  const rules = [{ required: true, message: "此为必填项" }];

  const handleCopyCallbackUrl = () => {
    const callbackUrl = `${window.location.origin}${LandrayOASSOCallbackRoute}`;
    copy(callbackUrl);
    Toast.success("回调地址已复制到剪贴板");
  };

  return (
    <>
      <Form.Section text="应用配置">
        <Row gutter={24}>
          <Col span={12}>
            <Form.Input
              label="应用ID"
              field="appId"
              trigger="blur"
              rules={rules}
            />
          </Col>
          <Col span={12}>
            <Form.Input
              label="应用Token"
              field="token"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={24}>
            <Form.Input
              label="信息同步地址"
              field="baseUrl"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={24}>
            <Form.Input
              label="信息查询地址"
              field="queryUri"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={12}>
            <Form.InputNumber
              label="同步超时"
              field="queryTimeout"
              suffix="秒"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={12}>
            <Form.InputNumber
              label="最大同步层级"
              field="maxLevel"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={24}>
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                回调地址
              </label>
              <Input
                value={`${window.location.origin}${LandrayOASSOCallbackRoute}`}
                disabled={true}
                suffix={
                  <Button
                    theme="borderless"
                    icon={<IconCopy />}
                    onClick={handleCopyCallbackUrl}
                    size="small"
                  />
                }
              />
              <div className="text-xs text-gray-500 mt-1">
                请将此地址配置到蓝凌OA系统中，用于单点登录回调
              </div>
            </div>
          </Col>
        </Row>
      </Form.Section>

      <Form.Section text="第三方认证信息同步配置">
        <Row gutter={24}>
          <Col span={12}>
            <Form.RadioGroup
              label="离职同步是否打开"
              field="statusIsOn"
              rules={rules}
            >
              {ISON_TYPE_MAP.map((item) => (
                <Form.Radio key={item.id} value={item.id}>
                  {item.name}
                </Form.Radio>
              ))}
            </Form.RadioGroup>
          </Col>
          <Col span={12}>
            <Form.RadioGroup
              label="账户同步是否打开"
              field="copyIsOn"
              rules={rules}
            >
              {ISON_TYPE_MAP.map((item) => (
                <Form.Radio key={item.id} value={item.id}>
                  {item.name}
                </Form.Radio>
              ))}
            </Form.RadioGroup>
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={12}>
            <Form.RadioGroup
              label="部门同步是否打开"
              field="departmentIsOn"
              rules={rules}
            >
              {ISON_TYPE_MAP.map((item) => (
                <Form.Radio key={item.id} value={item.id}>
                  {item.name}
                </Form.Radio>
              ))}
            </Form.RadioGroup>
          </Col>
        </Row>
      </Form.Section>
    </>
  );
};
