# 蓝凌单点登录

## OpenAPI Specification

```yaml
openapi: 3.0.1
info:
  title: ''
  description: ''
  version: 1.0.0
paths:
  /v1/system/lanling_login:
    post:
      summary: 蓝凌单点登录
      deprecated: false
      description: ''
      tags:
        - 01.系统管理
      parameters:
        - name: vdebug
          in: header
          description: 非登录态验证key-value
          required: false
          example: 6c3e8e4aa985fd0fa6d7a9bf1c3ded58
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                uuid:
                  type: string
                  title: uuid
              x-apifox-orders:
                - uuid
              x-apifox-refs: {}
              required:
                - uuid
              x-apifox-ignore-properties: []
            example:
              username: abc
              password: fsadfkfd
              verifyCode: 5k37
              verifyKey: fdasjkfa
      responses:
        '200':
          description: ''
          content:
            application/json:
              schema:
                type: object
                properties:
                  code:
                    type: integer
                    title: 结果
                    description: 0:成功 others:出错
                  message:
                    type: string
                    title: 说明
                    description: 当code！=0时是出错信息
                  data:
                    type: object
                    properties:
                      authToken:
                        type: string
                        title: 认证Token
                        description: 访问系统API是，使用此Token填充Authoration字段
                      expireTime:
                        type: string
                        title: 失效时间
                        description: 登录状态过期的时间，超过此时间需要重新登录
                      refreshToken:
                        type: string
                        title: 刷新Token
                        description: 认证token处于快失效状态时，用此token进行登录态续期
                      employee:
                        type: object
                        properties:
                          id:
                            type: integer
                            title: 新增时不用设置
                            x-apifox-mock: '@id'
                          name:
                            type: string
                            title: 姓名
                            x-apifox-mock: '@cname'
                          employeeId:
                            type: string
                            title: 工号
                            x-apifox-mock: '@string'
                        x-apifox-orders:
                          - 01H9R6R0CZ7C5GNZ4R85KY4963
                        x-apifox-refs:
                          01H9R6R0CZ7C5GNZ4R85KY4963: &ref_0
                            $ref: >-
                              #/components/schemas/%E5%91%98%E5%B7%A5%E7%99%BB%E5%BD%95%E8%BF%94%E5%9B%9E%E4%BF%A1%E6%81%AF(employee)%20
                        required:
                          - name
                          - employeeId
                        x-apifox-ignore-properties:
                          - id
                          - name
                          - employeeId
                    x-apifox-orders:
                      - 01H9MBPM6QKP2ER8G3GSPG983A
                    x-apifox-refs:
                      01H9MBPM6QKP2ER8G3GSPG983A:
                        $ref: >-
                          #/components/schemas/%E7%99%BB%E5%BD%95%E6%88%90%E5%8A%9F
                    required:
                      - authToken
                      - expireTime
                      - refreshToken
                      - employee
                    x-apifox-ignore-properties:
                      - authToken
                      - expireTime
                      - refreshToken
                      - employee
                x-apifox-orders:
                  - code
                  - message
                  - data
                required:
                  - code
                  - message
                  - data
                x-apifox-refs: {}
                x-apifox-ignore-properties: []
          headers: {}
          x-apifox-name: 成功
      security: []
      x-apifox-folder: 01.系统管理
      x-apifox-status: released
      x-run-in-apifox: https://app.apifox.com/web/project/3010052/apis/api-335674532-run
components:
  schemas:
    '员工登录返回信息(employee) ':
      type: object
      properties:
        id:
          type: integer
          title: 新增时不用设置
          x-apifox-mock: '@id'
        name:
          type: string
          title: 姓名
          x-apifox-mock: '@cname'
        employeeId:
          type: string
          title: 工号
          x-apifox-mock: '@string'
      x-apifox-orders:
        - id
        - name
        - employeeId
      required:
        - name
        - employeeId
      x-apifox-ignore-properties: []
      x-apifox-folder: ''
    登录成功:
      type: object
      properties:
        authToken:
          type: string
          title: 认证Token
          description: 访问系统API是，使用此Token填充Authoration字段
        expireTime:
          type: string
          title: 失效时间
          description: 登录状态过期的时间，超过此时间需要重新登录
        refreshToken:
          type: string
          title: 刷新Token
          description: 认证token处于快失效状态时，用此token进行登录态续期
        employee:
          type: object
          properties:
            id:
              type: integer
              title: 新增时不用设置
              x-apifox-mock: '@id'
            name:
              type: string
              title: 姓名
              x-apifox-mock: '@cname'
            employeeId:
              type: string
              title: 工号
              x-apifox-mock: '@string'
          x-apifox-orders:
            - 01H9R6R0CZ7C5GNZ4R85KY4963
          x-apifox-refs:
            01H9R6R0CZ7C5GNZ4R85KY4963: *ref_0
          required:
            - name
            - employeeId
          x-apifox-ignore-properties:
            - id
            - name
            - employeeId
      x-apifox-orders:
        - authToken
        - expireTime
        - refreshToken
        - employee
      required:
        - authToken
        - expireTime
        - refreshToken
        - employee
      x-apifox-refs: {}
      x-apifox-ignore-properties: []
      x-apifox-folder: ''
  securitySchemes: {}
servers: []
security: []

```
